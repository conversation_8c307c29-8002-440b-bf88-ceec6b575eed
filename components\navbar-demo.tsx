"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { Terminal, Menu as MenuIcon, X } from "lucide-react";

export function NavbarDemo() {
  return (
    <div className="relative w-full flex items-center justify-center">
      <HeroHeader className="top-2" />
    </div>
  );
}

// Export HeroHeader for direct use
export function HeroHeader({ className }: { className?: string }) {
  const [isScrolled, setIsScrolled] = useState(false);
  const [menuState, setMenuState] = useState(false);
  const router = useRouter();

  // Handle scroll effect for backdrop blur
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleLogin = () => {
    router.push("/auth");
  };

  const toggleMobileMenu = () => {
    setMenuState(!menuState);
  };

  return (
    <nav className="fixed top-0 left-0 right-0 z-20 font-mono">
      <div
        className={cn(
          "mx-auto transition-all duration-300",
          isScrolled
            ? "max-w-6xl bg-zinc-950/80 backdrop-blur-lg rounded-lg border border-zinc-800 mt-4 px-6 py-3"
            : "max-w-5xl px-6 py-4"
        )}
      >
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <Terminal className="w-5 h-5 text-white" />
            <span className="text-white font-mono uppercase tracking-wider font-bold">
              ALGOZ
            </span>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            <a
              href="/features"
              className="text-zinc-400 hover:text-white transition-colors duration-150 font-mono uppercase tracking-wider text-sm"
            >
              FEATURES
            </a>
            <a
              href="/solution"
              className="text-zinc-400 hover:text-white transition-colors duration-150 font-mono uppercase tracking-wider text-sm"
            >
              SOLUTION
            </a>
            <a
              href="/pricing"
              className="text-zinc-400 hover:text-white transition-colors duration-150 font-mono uppercase tracking-wider text-sm"
            >
              PRICING
            </a>
            <a
              href="/about"
              className="text-zinc-400 hover:text-white transition-colors duration-150 font-mono uppercase tracking-wider text-sm"
            >
              ABOUT
            </a>
          </div>

          {/* Desktop Login Button */}
          <button
            onClick={handleLogin}
            className="hidden lg:block bg-white text-black px-4 py-2 rounded font-mono text-sm font-medium hover:bg-zinc-200 transition-colors duration-150"
          >
            LOGIN
          </button>

          {/* Mobile Menu Button */}
          <button
            onClick={toggleMobileMenu}
            className="lg:hidden p-2 text-white"
            aria-label="Toggle menu"
          >
            <div className="relative w-6 h-6">
              <MenuIcon
                className={cn(
                  "absolute inset-0 w-6 h-6 transition-all duration-200",
                  menuState
                    ? "rotate-90 scale-0 opacity-0"
                    : "rotate-0 scale-100 opacity-100"
                )}
              />
              <X
                className={cn(
                  "absolute inset-0 w-6 h-6 transition-all duration-200",
                  menuState
                    ? "rotate-0 scale-100 opacity-100"
                    : "-rotate-90 scale-0 opacity-0"
                )}
              />
            </div>
          </button>
        </div>

        {/* Mobile Menu */}
        {menuState && (
          <div className="lg:hidden mt-4 p-4 bg-zinc-900 rounded-lg border border-zinc-800">
            <div className="space-y-4">
              <a
                href="/features"
                className="block text-zinc-400 hover:text-white transition-colors duration-150 font-mono uppercase tracking-wider text-sm"
              >
                FEATURES
              </a>
              <a
                href="/solution"
                className="block text-zinc-400 hover:text-white transition-colors duration-150 font-mono uppercase tracking-wider text-sm"
              >
                SOLUTION
              </a>
              <a
                href="/pricing"
                className="block text-zinc-400 hover:text-white transition-colors duration-150 font-mono uppercase tracking-wider text-sm"
              >
                PRICING
              </a>
              <a
                href="/about"
                className="block text-zinc-400 hover:text-white transition-colors duration-150 font-mono uppercase tracking-wider text-sm"
              >
                ABOUT
              </a>
              <button
                onClick={handleLogin}
                className="w-full bg-white text-black px-4 py-2 rounded font-mono text-sm font-medium hover:bg-zinc-200 transition-colors duration-150 mt-4"
              >
                LOGIN
              </button>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
