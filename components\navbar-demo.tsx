"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { Terminal, Menu as MenuIcon, X } from "lucide-react";

export function NavbarDemo() {
  return (
    <div className="relative w-full flex items-center justify-center">
      <HeroHeader />
    </div>
  );
}

// Smooth scroll utility function
const smoothScrollTo = (elementId: string) => {
  const element = document.getElementById(elementId);
  if (element) {
    const navbarHeight = 80; // Account for fixed navbar height
    const elementPosition = element.offsetTop - navbarHeight;

    window.scrollTo({
      top: elementPosition,
      behavior: 'smooth'
    });
  }
};

// Export HeroHeader for direct use
export function HeroHeader() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [menuState, setMenuState] = useState(false);
  const router = useRouter();

  // Handle scroll effect for backdrop blur
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleLogin = () => {
    router.push("/auth");
  };

  const toggleMobileMenu = () => {
    setMenuState(!menuState);
  };

  // Handle navigation clicks with smooth scrolling
  const handleNavClick = (href: string, e: React.MouseEvent) => {
    e.preventDefault();

    // Close mobile menu if open
    setMenuState(false);

    // Check if it's an anchor link
    if (href.startsWith('#')) {
      const elementId = href.substring(1);
      smoothScrollTo(elementId);
    } else if (href.startsWith('/')) {
      // Internal route navigation
      router.push(href);
    } else {
      // External link
      window.open(href, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 font-mono">
      <div
        className={cn(
          "mx-auto transition-all duration-300",
          isScrolled
            ? "max-w-6xl bg-zinc-950/80 backdrop-blur-lg rounded-lg border border-zinc-800 mt-4 px-6 py-3"
            : "max-w-5xl px-6 py-4"
        )}
      >
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <Terminal className="w-5 h-5 text-white" />
            <span className="text-white font-mono uppercase tracking-wider font-bold">
              ALGOZ
            </span>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            <button
              onClick={(e) => handleNavClick('#features', e)}
              className="text-zinc-400 hover:text-white transition-colors duration-150 font-mono uppercase tracking-wider text-sm cursor-pointer"
            >
              FEATURES
            </button>
            <button
              onClick={(e) => handleNavClick('#solution', e)}
              className="text-zinc-400 hover:text-white transition-colors duration-150 font-mono uppercase tracking-wider text-sm cursor-pointer"
            >
              SOLUTION
            </button>
            <button
              onClick={(e) => handleNavClick('#pricing', e)}
              className="text-zinc-400 hover:text-white transition-colors duration-150 font-mono uppercase tracking-wider text-sm cursor-pointer"
            >
              PRICING
            </button>
            <button
              onClick={(e) => handleNavClick('#about', e)}
              className="text-zinc-400 hover:text-white transition-colors duration-150 font-mono uppercase tracking-wider text-sm cursor-pointer"
            >
              ABOUT
            </button>
          </div>

          {/* Desktop Login Button */}
          <button
            onClick={handleLogin}
            className="hidden lg:block bg-white text-black px-4 py-2 rounded font-mono text-sm font-medium hover:bg-zinc-200 transition-colors duration-150"
          >
            LOGIN
          </button>

          {/* Mobile Menu Button */}
          <button
            onClick={toggleMobileMenu}
            className="lg:hidden p-2 text-white"
            aria-label="Toggle menu"
          >
            <div className="relative w-6 h-6">
              <MenuIcon
                className={cn(
                  "absolute inset-0 w-6 h-6 transition-all duration-200",
                  menuState
                    ? "rotate-90 scale-0 opacity-0"
                    : "rotate-0 scale-100 opacity-100"
                )}
              />
              <X
                className={cn(
                  "absolute inset-0 w-6 h-6 transition-all duration-200",
                  menuState
                    ? "rotate-0 scale-100 opacity-100"
                    : "-rotate-90 scale-0 opacity-0"
                )}
              />
            </div>
          </button>
        </div>

        {/* Mobile Menu */}
        {menuState && (
          <div className="lg:hidden mt-4 p-4 bg-zinc-900 rounded-lg border border-zinc-800">
            <div className="space-y-4">
              <button
                onClick={(e) => handleNavClick('#features', e)}
                className="block w-full text-left text-zinc-400 hover:text-white transition-colors duration-150 font-mono uppercase tracking-wider text-sm"
              >
                FEATURES
              </button>
              <button
                onClick={(e) => handleNavClick('#solution', e)}
                className="block w-full text-left text-zinc-400 hover:text-white transition-colors duration-150 font-mono uppercase tracking-wider text-sm"
              >
                SOLUTION
              </button>
              <button
                onClick={(e) => handleNavClick('#pricing', e)}
                className="block w-full text-left text-zinc-400 hover:text-white transition-colors duration-150 font-mono uppercase tracking-wider text-sm"
              >
                PRICING
              </button>
              <button
                onClick={(e) => handleNavClick('#about', e)}
                className="block w-full text-left text-zinc-400 hover:text-white transition-colors duration-150 font-mono uppercase tracking-wider text-sm"
              >
                ABOUT
              </button>
              <button
                onClick={handleLogin}
                className="w-full bg-white text-black px-4 py-2 rounded font-mono text-sm font-medium hover:bg-zinc-200 transition-colors duration-150 mt-4"
              >
                LOGIN
              </button>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
